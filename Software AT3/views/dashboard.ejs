<%- include('header.ejs') %>

<div class="container mt-4">
    <h1>Welcome, <%= user.name %></h1>
    
    <% if (user.role === 'Teacher') { %>
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3>Your Students</h3>
                    <a href="/create-player" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Create New Player
                    </a>
                </div>
                <% if (players && players.length > 0) { %>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Total Score</th>
                                    <th>Academic Score</th>
                                    <th>Effort Score</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% players.forEach(player => { %>
                                    <tr>
                                        <td><%= player.name %></td>
                                        <td><%= player.totalScore %></td>
                                        <td><%= player.academicScore %></td>
                                        <td><%= player.effortScore %></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="showUpdatePlayerModal('${player._id}', '${escapeHtml(player.name)}')">
                                                <i class="bi bi-pencil"></i> Update
                                            </button>
                                        </td>
                                    </tr>
                                <% }) %>
                            </tbody>
                        </table>
                    </div>
                <% } else { %>
                    <div class="alert alert-info">
                        <p>No students found. Start by adding students to your class.</p>
                        <a href="/create-player" class="btn btn-primary mt-2">Create New Player</a>
                    </div>
                <% } %>
            </div>
        </div>
    <% } else { %>
        <div class="row mt-4">
            <div class="col-md-12">
                <h3>Your Team</h3>
                <% if (typeof players !== 'undefined' && players && players.length > 0) { %>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Total Score</th>
                                    <th>Academic Score</th>
                                    <th>Effort Score</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% players.forEach(player => { %>
                                    <tr>
                                        <td><%= player.name %></td>
                                        <td><%= player.totalScore %></td>
                                        <td><%= player.academicScore %></td>
                                        <td><%= player.effortScore %></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary">Update</button>
                                        </td>
                                    </tr>
                                <% }) %>
                            </tbody>
                        </table>
                    </div>
                <% } else { %>
                    <p>No players found. Start by adding players to your team.</p>
                <% } %>
            </div>
        </div>
    <% } %>
</div>

<%- include('footer.ejs') %>